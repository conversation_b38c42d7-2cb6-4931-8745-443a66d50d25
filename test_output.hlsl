struct RayDesc
{
  float3 Origin;
  float3 Direction;
  float TMin;
  float TMax;
};

struct Struct_VertexBuffer
{
  float3 Position_;
  float3 Normal;
  float3 Color;
  float4 Tangent;
  float2 UV;
  int MaterialID;
  int ShapeID;
};

struct Struct__MeshViewerCB
{
  float3 CameraPos;
  float _padding0;
  float4x4 InvViewProjMtx;
};

RWTexture2D<float4> Output : register(u0);
uniform /* unknown type 43 */ Scene : register(t0);
StructuredBuffer<Struct_VertexBuffer> VertexBuffer : register(t1);
ConstantBuffer<Struct__MeshViewerCB> _MeshViewerCB : register(b0);
float3 fresnelSchlick(float cosTheta, float3 F0)
{
  return F0 + (1.0f - F0 * pow(1.0f - cosTheta, 5.0f));
}

float D_GGX(float NoH, float alpha)
{
  float alpha2 = (alpha * alpha);
  float d = ((NoH * NoH) * alpha2 - 1.0f) + 1.0f;
  return (alpha2 * 0.31830988618379067f) / (d * d);
}

float G1_GGX_Schlick(float NoV, float alpha)
{
  float k = alpha / 2.0f;
  return max(NoV, 0.001f) / (NoV * 1.0f - k) + k;
}

float G_Smith(float NoV, float NoL, float alpha)
{
  return (G1_GGX_Schlick(NoL, alpha) * G1_GGX_Schlick(NoV, alpha));
}

float3 microfacetBRDF(float3 L, float3 V, float3 N, float metallic, float roughness, float3 baseColor, float specularlevel)
{
  float3 H = normalize(V + L);
  float NoV = clamp(dot(N, V), 0.0f, 1.0f);
  float NoL = clamp(dot(N, L), 0.0f, 1.0f);
  float NoH = clamp(dot(N, H), 0.0f, 1.0f);
  float VoH = clamp(dot(V, H), 0.0f, 1.0f);
  float3 f0 = (0.16f * (specularlevel * specularlevel));
  f0 = lerp(f0, baseColor, metallic);
  float alpha = (roughness * roughness);
  float3 F = fresnelSchlick(VoH, f0);
  float D = D_GGX(NoH, alpha);
  float G = G_Smith(NoV, NoL, alpha);
  float3 specular = ((F * D) * G) / ((4.0f * max(NoV, 0.001f)) * max(NoL, 0.001f));
  float3 rhoD = baseColor;
  rhoD *= 1.0f - F;
  rhoD *= 1.0f - metallic;
  float3 diffuse = (rhoD * 0.31830988618379067f);
  return diffuse + specular;
}

[numthreads(8, 8, 1)]
void main(uint3 DTid : SV_DispatchThreadID)
{
  uint2 px = DTid.xy;
  uint w;
  uint h;
  Output.GetDimensions(w, h);
  float2 dimensions = float2(w, h);
  float2 screenPos = (float2(px) + 0.5f / dimensions * 2.0f) - 1.0f;
  screenPos.y = (-screenPos.y);
  float4 world = mul(float4(screenPos, 0.99f, 1), _MeshViewerCB.InvViewProjMtx);
  world.xyz /= world.w;
  RayDesc ray;
  ray.Origin = _MeshViewerCB.CameraPos;
  ray.TMin = 0.0f;
  ray.TMax = 10000.0f;
  ray.Direction = normalize(world.xyz - ray.Origin);
  /* unknown type 44 */ rayQuery;
  rayQuery.TraceRayInline(Scene, 0, 255, ray);
  rayQuery.Proceed();
  uint rayStatus = rayQuery.CommittedStatus();
  if (rayStatus == unknown_var)
  {
    float3 barycentrics;
    barycentrics.yz = rayQuery.CommittedTriangleBarycentrics();
    barycentrics.x = 1.0f - barycentrics.y + barycentrics.z;
    uint primitiveIndex = rayQuery.CommittedPrimitiveIndex();
    uint3 indices = uint3((primitiveIndex * 3) + 0, (primitiveIndex * 3) + 1, (primitiveIndex * 3) + 2);
    float3 normal0 = VertexBuffer[indices.x].Normal;
    float3 normal1 = VertexBuffer[indices.y].Normal;
    float3 normal2 = VertexBuffer[indices.z].Normal;
    float3 normal = (normalize(normal0) * barycentrics.x) + (normalize(normal1) * barycentrics.y) + (normalize(normal2) * barycentrics.z);
    normal = normalize(normal);
    float3 hitPoint = ray.Origin + (ray.Direction * rayQuery.CommittedRayT());
    float3 lightPosition = float3(2.0f, 3.0f, 2.0f);
    float3 lightColor = float3(1.0f, 1.0f, 1.0f);
    float lightIntensity = 2.0f;
    float3 basecolor = float3(0.8f, 0.8f, 0.8f);
    float roughness = 0.4f;
    float specularlevel = 0.5f;
    float metallic = 0.0f;
    float3 lightDirection = normalize(lightPosition - hitPoint);
    float distanceToLight = length(lightPosition - hitPoint);
    float attenuation = 1.0f / 1.0f + (distanceToLight * 0.1f);
    float3 lightPerpendicularIrradiance = ((lightIntensity * lightColor) * attenuation);
    float3 viewDirection = normalize(_MeshViewerCB.CameraPos - hitPoint);
    float3 irradiance = (max(dot(lightDirection, normal), 0.0f) * lightPerpendicularIrradiance);
    float3 brdf = microfacetBRDF(lightDirection, viewDirection, normal, metallic, roughness, basecolor, specularlevel);
    float3 radiance = (irradiance * brdf);
    radiance += float3(0.02f, 0.02f, 0.02f);
    Output[px] = float4(radiance, 1.0f);
  }
  else
  {
    Output[px] = float4(0.0f, 0.0f, 0.0f, 0.0f);
  }
}

