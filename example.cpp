#include <iostream>
#include <fstream>
#include <string>
#include <cstdio>
#include <cstdlib>
#include "src/hlsloptconv.h"

// Memory management functions required by hlsloptconv_core
extern "C" void* chkmalloc(size_t sz) { return malloc(sz); }
extern "C" void chkfree(void* p) { free(p); }

// Helper function to read file contents
std::string ReadFileContents(const char* filename)
{
    std::ifstream file(filename, std::ios::binary);
    if (!file.is_open())
    {
        std::cerr << "Error: Could not open file " << filename << std::endl;
        return "";
    }
    
    std::string content((std::istreambuf_iterator<char>(file)),
                        std::istreambuf_iterator<char>());
    file.close();
    return content;
}

// Helper function to write file contents
bool WriteFileContents(const char* filename, const std::string& content)
{
    std::ofstream file(filename, std::ios::binary);
    if (!file.is_open())
    {
        std::cerr << "Error: Could not create output file " << filename << std::endl;
        return false;
    }
    
    file.write(content.c_str(), content.size());
    file.close();
    return true;
}

// Text output callback for capturing generated code
void WriteStr_String(const char* str, size_t size, void* userData)
{
    std::string* output = static_cast<std::string*>(userData);
    output->append(str, size);
}

int main(int argc, char** argv)
{
    // Default parameters - you can modify these for testing
    const char* inputFile = "D:/XuSong/Project/shaderProfile/shader/RGA_Test/RayTraceCS.hlsl";
    const char* outputFile = "D:/XuSong/Project/shaderProfile/hlsloptconv/test_output.hlsl";
    const char* shaderStage = "compute"; // vertex, pixel, compute
    
    // Allow command line override
    if (argc >= 2) inputFile = argv[1];
    if (argc >= 3) outputFile = argv[2];
    if (argc >= 4) shaderStage = argv[3];
    
    std::cout << "HLSL Optimizer Example" << std::endl;
    std::cout << "Input file: " << inputFile << std::endl;
    std::cout << "Output file: " << outputFile << std::endl;
    std::cout << "Shader stage: " << shaderStage << std::endl;
    std::cout << "------------------------" << std::endl;
    
    // Read input file
    std::string inputCode = ReadFileContents(inputFile);
    if (inputCode.empty())
    {
        std::cerr << "Failed to read input file or file is empty" << std::endl;
        return 1;
    }
    
    std::cout << "Input file size: " << inputCode.size() << " bytes" << std::endl;
    
    // Setup HOC_Config
    HOC_Config config;
    
    // Set shader stage
    if (strcmp(shaderStage, "vertex") == 0)
        config.stage = HOC_ShaderStage_Vertex;
    else if (strcmp(shaderStage, "pixel") == 0)
        config.stage = HOC_ShaderStage_Pixel;
    else if (strcmp(shaderStage, "compute") == 0)
        config.stage = HOC_ShaderStage_Compute;
    else
    {
        std::cerr << "Error: Unknown shader stage '" << shaderStage << "'. Use vertex, pixel, or compute." << std::endl;
        return 1;
    }
    
    // Set output format to modern HLSL
    config.outputFmt = HOC_OSF_HLSL_SM6;
    
    // Set output flags
    config.outputFlags = 
        HOC_OF_GLSL_RENAME_PSOUTPUT |
        HOC_OF_GLSL_RENAME_SAMPLERS |
        HOC_OF_GLSL_RENAME_CBUFFERS |
        HOC_OF_GLSL_RENAME_VSINPUT |
        HOC_OF_GLSL_RENAME_VARYINGS;
    
    // Setup output capture
    std::string generatedCode;
    HOC_TextOutput codeOutput = { &WriteStr_String, &generatedCode };
    config.codeOutputStream = &codeOutput;
    
    // Setup error output capture
    std::string errorOutput;
    HOC_TextOutput errorTextOutput = { &WriteStr_String, &errorOutput };
    config.errorOutputStream = &errorTextOutput;
    
    // No defines for this example
    config.defines = nullptr;
    
    // No include file loading for this example
    config.loadIncludeFileFunc = nullptr;
    config.loadIncludeFileUserData = nullptr;
    
    // No AST dump for this example
    config.ASTDumpStream = nullptr;
    
    // No interface output for this example
    config.interfaceOutput = nullptr;
    
    std::cout << "Starting compilation..." << std::endl;

    // Call the compiler - this is where you can set breakpoints for debugging
    bool success = HOC_CompileShader(inputFile, inputCode.c_str(), &config);

    // Debug: Print some information about the compilation
    std::cout << "DEBUG: Compilation finished with result: " << (success ? "SUCCESS" : "FAILED") << std::endl;
    
    std::cout << "Compilation result: " << (success ? "SUCCESS" : "FAILED") << std::endl;

    // Print error output if any
    if (!errorOutput.empty())
    {
        std::cout << "Error output:" << std::endl;
        std::cout << errorOutput << std::endl;
    }

    if (success)
    {
        std::cout << "Generated code size: " << generatedCode.size() << " bytes" << std::endl;
        
        // Write output file
        if (WriteFileContents(outputFile, generatedCode))
        {
            std::cout << "Output written to: " << outputFile << std::endl;
        }
        else
        {
            std::cerr << "Failed to write output file" << std::endl;
            return 1;
        }
    }
    else
    {
        std::cout << "Compilation failed. Generated placeholder code size: " << generatedCode.size() << " bytes" << std::endl;
        
        // Even if compilation failed, try to write the placeholder output
        if (!generatedCode.empty())
        {
            if (WriteFileContents(outputFile, generatedCode))
            {
                std::cout << "Placeholder output written to: " << outputFile << std::endl;
            }
        }
        else
        {
            std::cout << "No output generated" << std::endl;
        }
    }
    
    std::cout << "Done." << std::endl;
    return success ? 0 : 1;
}
