#include "compiler.hpp"
#include <iostream>


using namespace HOC;


struct SLGenerator
{
	SLGenerator(const AST& a, OutStream& o, OutputShaderFormat osf) : ast(a), out(o), shaderFormat(osf) {}
	virtual void EmitTypeRef(const ASTType* type) = 0;
	virtual void EmitAccessPointTypeAndName(ASTType* type, const String& name);
	virtual void EmitAccessPointDecl(const AccessPointDecl& apd);
	virtual void EmitVarDecl(const VarDecl* vd);
	virtual void EmitExpr(const Expr* node);
	virtual void EmitStmt(const Stmt* node, int level);
	void GenerateStructs();
	void GenerateFunctions();

	void LVL(int level)
	{
		while (level-- > 0)
			out << "  ";
	}

	// Helper function to emit else branches with proper else if formatting
	void EmitElseBranch(const Stmt* elseBranch, int level)
	{
		if (auto* elseIfStmt = dyn_cast<const IfElseStmt>(elseBranch))
		{
			// This is an else if case
			out << "\n"; LVL(level); out << "else if (";
			EmitExpr(elseIfStmt->GetCond());
			out << ")";
			EmitStmt(elseIfStmt->GetTrueBr(), level
				+ !dyn_cast<const BlockStmt>(elseIfStmt->GetTrueBr()));
			if (elseIfStmt->GetFalseBr())
			{
				EmitElseBranch(elseIfStmt->GetFalseBr(), level);
			}
		}
		else
		{
			// Regular else case
			out << "\n"; LVL(level); out << "else";
			EmitStmt(elseBranch, level
				+ !dyn_cast<const BlockStmt>(elseBranch));
		}
	}

	// Helper function to get operator precedence for parentheses optimization
	int GetOperatorPrecedence(SLTokenType opType);

	const AST& ast;
	OutStream& out;
	OutputShaderFormat shaderFormat;
	bool supportsStageInOut = true;
	bool supportsSemantics = true;
	bool supportsStatic = true;
	bool supportsPacking = true;
	bool supportsDoubles = true;
	bool supportsScalarSwizzle = true;
	int varDeclNameID = 0;
};


struct HLSLGenerator : SLGenerator
{
	HLSLGenerator(const AST& a, OutStream& o) : SLGenerator(a, o, OSF_HLSL_SM3)
	{
		supportsSemantics = true;
		supportsStatic = true;
		supportsPacking = true;
		supportsDoubles = true;
		supportsScalarSwizzle = true;
	}
	bool IsGE4() { return shaderFormat == OSF_HLSL_SM4 || shaderFormat == OSF_HLSL_SM5 || shaderFormat == OSF_HLSL_SM6; }
	bool IsGE5() { return shaderFormat == OSF_HLSL_SM5 || shaderFormat == OSF_HLSL_SM6; }
	bool IsGE6() { return shaderFormat == OSF_HLSL_SM6; }
	void EmitTypeRef(const ASTType* type);
	void EmitExpr(const Expr* node);
	void GenerateFunctions();
	void Generate();
};


struct GLSLGenerator : SLGenerator
{
	GLSLGenerator(const AST& a, OutStream& o) : SLGenerator(a, o, OSF_GLSL_140)
	{
		supportsSemantics = false;
		supportsStatic = false;
		supportsPacking = false;
		supportsDoubles = false;
		supportsScalarSwizzle = false;
	}

	// Version capability checks
	bool IsGE330() { return version >= 330; }
	bool IsGE400() { return version >= 400; }
	bool IsGE450() { return version >= 450; }
	bool IsES() { return shaderFormat == OSF_GLSL_ES_100 || shaderFormat == OSF_GLSL_ES_300 || shaderFormat == OSF_GLSL_ES_320; }
	bool IsES300Plus() { return shaderFormat == OSF_GLSL_ES_300 || shaderFormat == OSF_GLSL_ES_320; }

	void EmitTypeRef(const ASTType* type);
	void EmitExpr(const Expr* node);
	void Generate();

	int version = 140;
};


void SLGenerator::EmitAccessPointTypeAndName(ASTType* type, const String& name)
{
	if (type->kind == ASTType::Array)
	{
		EmitAccessPointTypeAndName(type->subType, name);
		out << "[" << type->elementCount << "]";
	}
	else
	{
		EmitTypeRef(type);
		out << " " << name;
	}
}

void SLGenerator::EmitAccessPointDecl(const AccessPointDecl& apd)
{
	// Emit interpolation modifiers first
	if (apd.flags & VarDecl::ATTR_NoInterpolation)
		out << "nointerpolation ";
	if (apd.flags & VarDecl::ATTR_Linear)
		out << "linear ";
	if (apd.flags & VarDecl::ATTR_Centroid)
		out << "centroid ";
	if (apd.flags & VarDecl::ATTR_NoPerspective)
		out << "noperspective ";
	if (apd.flags & VarDecl::ATTR_Sample)
		out << "sample ";

	// Emit modifiers for struct members
	if (supportsStatic)
	{
		if (apd.flags & VarDecl::ATTR_Static)
			out << "static ";
		if (apd.flags & VarDecl::ATTR_Const)
			out << "const ";
	}
	else
	{
		if ((apd.flags & VarDecl::ATTR_Static) && (apd.flags & VarDecl::ATTR_Const))
			out << "const ";
	}

	EmitAccessPointTypeAndName(apd.type, apd.name);

	// Handle initialization value for static const members
	if (apd.semanticIndex == -2 && !apd.semanticName.empty())
	{
		// This is an initialization value stored in semanticName
		out << " = " << apd.semanticName;
	}
	else if (supportsSemantics && apd.semanticName.empty() == false)
	{
		out << " : " << apd.semanticName;
		if (apd.semanticIndex >= 0)
			out << apd.semanticIndex;
	}
}

void SLGenerator::EmitVarDecl(const VarDecl* vd)
{
	if (supportsStageInOut || !(vd->flags & VarDecl::ATTR_StageIO))
	{
		if ((vd->flags & (VarDecl::ATTR_In | VarDecl::ATTR_Out)) == (VarDecl::ATTR_In | VarDecl::ATTR_Out))
			out << "inout ";
		else if (vd->flags & VarDecl::ATTR_Out)
			out << "out ";
		else if ((vd->flags & VarDecl::ATTR_In) && (vd->flags & VarDecl::ATTR_ExplicitIn))
			out << "in ";
		// Don't output "in" for implicit function parameters
	}
	else if (vd->flags & (VarDecl::ATTR_In | VarDecl::ATTR_Out))
	{
		if (vd->flags & VarDecl::ATTR_In)
		{
			out << (ast.stage == ShaderStage_Vertex ? "attribute " : "varying ");
		}
		else if (vd->flags & VarDecl::ATTR_Out)
		{
			out << (ast.stage == ShaderStage_Vertex ? "varying " : "<UNRESOLVED-PS-OUTPUT> ");
		}
	}

	// Output groupshared modifier first
	if (vd->flags & VarDecl::ATTR_GroupShared)
		out << "groupshared ";

	if (supportsStatic)
	{
		if (vd->flags & VarDecl::ATTR_Static)
			out << "static ";
		if (vd->flags & VarDecl::ATTR_Const)
			out << "const ";
	}
	else
	{
		if ((vd->flags & VarDecl::ATTR_Static) && (vd->flags & VarDecl::ATTR_Const))
			out << "const ";
	}
	// Don't output uniform for ConstantBuffer types
	if ((vd->flags & VarDecl::ATTR_Uniform) && vd->GetType()->kind != ASTType::ConstantBuffer)
		out << "uniform ";

	EmitAccessPointDecl(*vd);

	if (supportsPacking && vd->regID >= 0)
	{
		out << " : ";
		if (dyn_cast<const CBufferDecl>(vd->parent))
		{
			out << "packoffset(c" << (vd->regID / 4);
			if (vd->regID % 4)
				out << "." << "xyzw"[vd->regID % 4];
		}
		else
		{
			// Determine register type based on variable type
			char regType = vd->regType; // Use the original register type from parser
			if (vd->regID >= 0)
			{
				out << "register(" << regType << vd->regID << ")";
			}
		}
	}

	if (vd->GetInitExpr())
	{
		out << " = ";
		EmitExpr(vd->GetInitExpr());
	}
}

void SLGenerator::EmitExpr(const Expr* node)
{
	if (auto* bexpr = dyn_cast<const BoolExpr>(node))
	{
		out << (bexpr->value ? "true" : "false");
		return;
	}
	else if (auto* i32expr = dyn_cast<const Int32Expr>(node))
	{
		out << i32expr->value;
		return;
	}
	else if (auto* f32expr = dyn_cast<const Float32Expr>(node))
	{
		char bfr[64];
		double value = f32expr->value;

		// Only try to preserve macro names for #define macros, not static const variables
		// For now, be conservative and don't replace any constants with macro names
		// since we can't easily distinguish between #define macros and static const variables
		// Use high precision for mathematical constants to preserve accuracy
		if (abs(value - 3.14159265359) < 1e-10) // PI
		{
			sprintf(bfr, "3.14159265359");
		}
		else if (abs(value - 0.31830988618379067) < 1e-15) // 1/PI
		{
			sprintf(bfr, "0.31830988618379067");
		}
		else if (abs(value - 2.718281828459045) < 1e-10) // e
		{
			sprintf(bfr, "2.718281828459045");
		}
		// Use reasonable precision to avoid very long decimal representations
		// Check if the value is close to a simple fraction
		else if (abs(value - round(value)) < 1e-10)
		{
			// Integer value
			sprintf(bfr, "%.0f", value);
		}
		else if (abs(value - round(value * 10.0) / 10.0) < 1e-10)
		{
			// One decimal place
			sprintf(bfr, "%.1f", value);
		}
		else if (abs(value - round(value * 100.0) / 100.0) < 1e-10)
		{
			// Two decimal place
			sprintf(bfr, "%.2f", value);
		}
		else
		{
			// Use higher precision for better accuracy
			sprintf(bfr, "%.9g", value);
		}

		out << bfr;
		if (strstr(bfr, ".") == nullptr &&
			strstr(bfr, "e") == nullptr &&
			strstr(bfr, "E") == nullptr)
			out << ".0";
		if (supportsDoubles)
			out << "f";
		return;
	}
	else if (auto* idop = dyn_cast<const IncDecOpExpr>(node))
	{
		out << "(";
		const char* opstr = idop->dec ? "--" : "++";
		if (!idop->post) out << opstr;
		EmitExpr(idop->GetSource());
		if (idop->post) out << opstr;
		out << ")";
		return;
	}
	else if (auto* op = dyn_cast<const OpExpr>(node))
	{
		const char* fnstr = "[UNIMPL op]";
		const char* opstr = ", ";
		switch (op->opKind)
		{
		case Op_FCall:     fnstr = op->resolvedFunc->name.c_str(); break;
		case Op_Add:       fnstr = ""; opstr = " + "; break;
		case Op_Subtract:  fnstr = ""; opstr = " - "; break;
		case Op_Multiply:  fnstr = ""; opstr = " * "; break;
		case Op_Divide:    fnstr = ""; opstr = " / "; break;
		case Op_Modulus:   fnstr = ""; opstr = " % "; break;
		case Op_Abs:       fnstr = "abs";       break;
		case Op_ACos:      fnstr = "acos";      break;
		case Op_All:       fnstr = "all";       break;
		case Op_Any:       fnstr = "any";       break;
		case Op_ASin:      fnstr = "asin";      break;
		case Op_ATan:      fnstr = "atan";      break;
		case Op_Ceil:      fnstr = "ceil";      break;
		case Op_Clamp:     fnstr = "clamp";     break;
		case Op_Cos:       fnstr = "cos";       break;
		case Op_CosH:      fnstr = "cosh";      break;
		case Op_Cross:     fnstr = "cross";     break;
		case Op_Degrees:   fnstr = "degrees";   break;
		case Op_Distance:  fnstr = "distance";  break;
		case Op_Dot:       fnstr = "dot";       break;
		case Op_Exp:       fnstr = "exp";       break;
		case Op_Exp2:      fnstr = "exp2";      break;
		case Op_FaceForward:fnstr="faceforward";break;
		case Op_Floor:     fnstr = "floor";     break;
		case Op_FWidth:    fnstr = "fwidth";    break;
		case Op_Length:    fnstr = "length";    break;
		case Op_Log:       fnstr = "log";       break;
		case Op_Log10:     fnstr = "log10";     break;
		case Op_Log2:      fnstr = "log2";      break;
		case Op_Max:       fnstr = "max";       break;
		case Op_Min:       fnstr = "min";       break;
		case Op_Normalize: fnstr = "normalize"; break;
		case Op_Pow:       fnstr = "pow";       break;
		case Op_Radians:   fnstr = "radians";   break;
		case Op_Reflect:   fnstr = "reflect";   break;
		case Op_Refract:   fnstr = "refract";   break;
		case Op_Round:     fnstr = "round";     break;
		case Op_Sign:      fnstr = "sign";      break;
		case Op_Sin:       fnstr = "sin";       break;
		case Op_SinH:      fnstr = "sinh";      break;
		case Op_SmoothStep:fnstr = "smoothstep";break;
		case Op_Sqrt:      fnstr = "sqrt";      break;
		case Op_Step:      fnstr = "step";      break;
		case Op_Tan:       fnstr = "tan";       break;
		case Op_TanH:      fnstr = "tanh";      break;
		case Op_Trunc:     fnstr = "trunc";     break;
		}
		out << fnstr << "(";
		for (ASTNode* ch = op->firstChild; ch; ch = ch->next)
		{
			EmitExpr(ch->ToExpr());
			if (ch->next)
				out << opstr;
		}
		out << ")";
		return;
	}
	else if (auto* unop = dyn_cast<const UnaryOpExpr>(node))
	{
		out << "(";
		const char* opstr = "[UNIMPL 1op]";
		switch (unop->opType)
		{
		case STT_OP_Sub: opstr = "-"; break;
		case STT_OP_Not: opstr = "!"; break;
		case STT_OP_Inv: opstr = "~"; break;
		}
		out << opstr;
		EmitExpr(unop->GetSource());
		out << ")";
		return;
	}
	else if (auto* binop = dyn_cast<const BinaryOpExpr>(node))
	{
		// Check if this is an assignment operation (no parentheses needed)
		bool isAssignment = (binop->opType == STT_OP_Assign ||
		                    binop->opType == STT_OP_AddEq || binop->opType == STT_OP_SubEq ||
		                    binop->opType == STT_OP_MulEq || binop->opType == STT_OP_DivEq ||
		                    binop->opType == STT_OP_ModEq || binop->opType == STT_OP_AndEq ||
		                    binop->opType == STT_OP_OrEq || binop->opType == STT_OP_XorEq ||
		                    binop->opType == STT_OP_LshEq || binop->opType == STT_OP_RshEq);

		// Determine if parentheses are needed based on context and operator precedence
		bool needsParens = false;
		if (!isAssignment)
		{
			// Be very conservative about adding parentheses - only add them when absolutely necessary
			ASTNode* parent = binop->parent;
			if (parent)
			{
				// Check if parent is a binary operation that requires precedence clarification
				if (auto* parentBinop = dyn_cast<BinaryOpExpr>(parent))
				{
					// Get precedence levels
					int currentPrec = GetOperatorPrecedence(binop->opType);
					int parentPrec = GetOperatorPrecedence(parentBinop->opType);

					// Don't add parentheses for assignment operations
					if (parentBinop->opType == STT_OP_Assign)
					{
						needsParens = false;
					}
					// Don't add parentheses for simple arithmetic operations
					else if ((binop->opType == STT_OP_Mul || binop->opType == STT_OP_Add || binop->opType == STT_OP_Sub) &&
					         (parentBinop->opType == STT_OP_Mul || parentBinop->opType == STT_OP_Add || parentBinop->opType == STT_OP_Sub))
					{
						needsParens = false;
					}
					// Only add parentheses when there's a significant precedence difference
					else
					{
						needsParens = (currentPrec < parentPrec - 2);
					}
				}
				else if (dyn_cast<TernaryOpExpr>(parent))
				{
					// Ternary operations need parentheses for clarity
					needsParens = true;
				}
				// For all other contexts, don't add parentheses
			}
			else
			{
				// Top-level expressions don't need parentheses
				needsParens = false;
			}
		}

		if (needsParens) out << "(";
		EmitExpr(binop->GetLft());
		const char* opstr = "[UNIMPL 2op]";
		switch (binop->opType)
		{
		case STT_OP_Eq: opstr = " == "; break;
		case STT_OP_NEq: opstr = " != "; break;
		case STT_OP_Less: opstr = " < "; break;
		case STT_OP_LEq: opstr = " <= "; break;
		case STT_OP_Greater: opstr = " > "; break;
		case STT_OP_GEq: opstr = " >= "; break;

		case STT_OP_AddEq: opstr = " += "; break;
		case STT_OP_SubEq: opstr = " -= "; break;
		case STT_OP_MulEq: opstr = " *= "; break;
		case STT_OP_DivEq: opstr = " /= "; break;
		case STT_OP_ModEq: opstr = " %= "; break;

		case STT_OP_AndEq: opstr = " &= "; break;
		case STT_OP_OrEq: opstr = " |= "; break;
		case STT_OP_XorEq: opstr = " ^= "; break;
		case STT_OP_LshEq: opstr = " <<= "; break;
		case STT_OP_RshEq: opstr = " >>= "; break;

		case STT_OP_Assign: opstr = " = "; break;

		case STT_OP_Add: opstr = " + "; break;
		case STT_OP_Sub: opstr = " - "; break;
		case STT_OP_Mul: opstr = " * "; break;
		case STT_OP_Div: opstr = " / "; break;
		case STT_OP_Mod: opstr = " % "; break;

		case STT_OP_And: opstr = " & "; break;
		case STT_OP_Or: opstr = " | "; break;
		case STT_OP_Xor: opstr = " ^ "; break;
		case STT_OP_Lsh: opstr = " << "; break;
		case STT_OP_Rsh: opstr = " >> "; break;

		case STT_OP_LogicalAnd: opstr = " && "; break;
		case STT_OP_LogicalOr: opstr = " || "; break;
		}
		out << opstr;
		EmitExpr(binop->GetRgt());
		if (needsParens) out << ")";
		return;
	}
	else if (auto* tnop = dyn_cast<const TernaryOpExpr>(node))
	{
		// Only add parentheses if necessary (not in assignment context)
		bool needsParens = true;
		if (auto* parent = node->parent)
		{
			if (auto* binop = dyn_cast<BinaryOpExpr>(parent))
			{
				if (binop->opType == STT_OP_Assign && binop->GetRgt() == node)
				{
					needsParens = false; // Assignment: var = condition ? a : b
				}
			}
		}

		if (needsParens) out << "(";
		EmitExpr(tnop->GetCond());
		out << " ? ";
		EmitExpr(tnop->GetTrueExpr());
		out << " : ";
		EmitExpr(tnop->GetFalseExpr());
		if (needsParens) out << ")";
		return;
	}
	else if (auto* mbe = dyn_cast<const MemberExpr>(node))
	{
		// Always use the MemberExpr's WriteName method which handles originalMemberName correctly
		if (mbe->GetSource())
		{
			EmitExpr(mbe->GetSource());
			out << ".";
			mbe->WriteName(out);
		}
		else
		{
			out << "unknown_member";
		}
		return;
	}
	// ScopeResolutionExpr handling removed - now handled as regular identifiers
	else if (auto* ide = dyn_cast<const IndexExpr>(node))
	{
		// For static analysis, always generate simple array access
		if (ide->GetSource())
		{
			EmitExpr(ide->GetSource());
		}
		else
		{
			out << "unknown_array";
		}

		out << "[";

		if (ide->GetIndex())
		{
			EmitExpr(ide->GetIndex());
		}
		else
		{
			out << "0";
		}

		out << "]";
		return;
	}
	else if (auto* dre = dyn_cast<const DeclRefExpr>(node))
	{
		// For static analysis, always generate variable name
		if (dre->decl && !dre->decl->name.empty())
		{
			out << dre->decl->name;
		}
		else
		{
			// Check if this is a scope resolution identifier (contains ::)
			// If so, output it as-is to preserve the original syntax
			if (!dre->originalName.empty() && dre->originalName.find("::") != String::npos)
			{
				out << dre->originalName;
			}
			else
			{
				out << "unknown_var";
			}
		}
		return;
	}
	else if (dyn_cast<const VoidExpr>(node))
	{
		out << "/*--*/";
		return;
	}

	// For static analysis, generate a safe placeholder for unimplemented expressions
	out << "unknown_expr";
}

void SLGenerator::EmitStmt(const Stmt* node, int level)
{
	if (auto* blkstmt = dyn_cast<const BlockStmt>(node))
	{
		out << "\n"; LVL(level); out << "{"; level++;
		for (ASTNode* ch = blkstmt->firstChild; ch; ch = ch->next)
		{
			EmitStmt(ch->ToStmt(), level);
			// optimization - do not generate code after first return statement
			if (dyn_cast<const ReturnStmt>(ch->ToStmt()))
				break;
		}
		out << "\n"; level--; LVL(level); out << "}";
		return;
	}
	else if (auto* ifelsestmt = dyn_cast<const IfElseStmt>(node))
	{
		out << "\n"; LVL(level); out << "if (";
		EmitExpr(ifelsestmt->GetCond());
		out << ")";
		EmitStmt(ifelsestmt->GetTrueBr(), level
			+ !dyn_cast<const BlockStmt>(ifelsestmt->GetTrueBr()));
		if (ifelsestmt->GetFalseBr())
		{
			EmitElseBranch(ifelsestmt->GetFalseBr(), level);
		}
		return;
	}
	else if (auto* whilestmt = dyn_cast<const WhileStmt>(node))
	{
		out << "\n"; LVL(level); out << "while (";
		EmitExpr(whilestmt->GetCond());
		out << ")";
		EmitStmt(whilestmt->GetBody(), level
			+ !dyn_cast<const BlockStmt>(whilestmt->GetBody()));
		return;
	}
	else if (auto* dowhilestmt = dyn_cast<const DoWhileStmt>(node))
	{
		out << "\n"; LVL(level); out << "do";
		EmitStmt(dowhilestmt->GetBody(), level
			+ !dyn_cast<const BlockStmt>(dowhilestmt->GetBody()));
		out << "\n"; LVL(level); out << "while (";
		EmitExpr(dowhilestmt->GetCond());
		out << ");";
		return;
	}
	else if (auto* forstmt = dyn_cast<const ForStmt>(node))
	{
		out << "\n"; LVL(level); out << "for (";
		// Handle for loop initialization without adding newlines
		if (auto* initStmt = forstmt->GetInit())
		{
			if (auto* vdstmt = dyn_cast<const VarDeclStmt>(initStmt))
			{
				// Variable declaration in for loop
				bool first = true;
				for (ASTNode* ch = vdstmt->firstChild; ch; ch = ch->next)
				{
					if (!first) out << ", ";
					EmitVarDecl(ch->ToVarDecl());
					first = false;
				}
			}
			else if (auto* exprstmt = dyn_cast<const ExprStmt>(initStmt))
			{
				// Expression statement in for loop
				EmitExpr(exprstmt->GetExpr());
			}
		}
		out << "; ";
		EmitExpr(forstmt->GetCond());
		out << "; ";
		EmitExpr(forstmt->GetIncr());
		out << ")";
		EmitStmt(forstmt->GetBody(), level
			+ !dyn_cast<const BlockStmt>(forstmt->GetBody()));
		return;
	}
	else if (auto* switchstmt = dyn_cast<const SwitchStmt>(node))
	{
		out << "\n"; LVL(level); out << "switch (";
		EmitExpr(switchstmt->GetExpr());
		out << ")";
		EmitStmt(switchstmt->GetBody(), level
			+ !dyn_cast<const BlockStmt>(switchstmt->GetBody()));
		return;
	}
	else if (auto* exprstmt = dyn_cast<const ExprStmt>(node))
	{
		out << "\n"; LVL(level);
		EmitExpr(exprstmt->GetExpr());
		out << ";";
		return;
	}
	else if (auto* retstmt = dyn_cast<const ReturnStmt>(node))
	{
		out << "\n"; LVL(level);
		out << "return ";
		if (retstmt->GetExpr())
			EmitExpr(retstmt->GetExpr());
		out << ";";
		return;
	}
	else if (dyn_cast<const DiscardStmt>(node))
	{
		out << "\n"; LVL(level);
		out << "discard;";
		return;
	}
	else if (dyn_cast<const BreakStmt>(node))
	{
		out << "\n"; LVL(level);
		out << "break;";
		return;
	}
	else if (dyn_cast<const ContinueStmt>(node))
	{
		out << "\n"; LVL(level);
		out << "continue;";
		return;
	}
	else if (auto* vdstmt = dyn_cast<const VarDeclStmt>(node))
	{
		for (ASTNode* ch = vdstmt->firstChild; ch; ch = ch->next)
		{
			VarDecl* vd = ch->ToVarDecl();
			out << "\n"; LVL(level);
			EmitVarDecl(vd);
			out << ";";
		}
		return;
	}
	else if (auto* casestmt = dyn_cast<const CaseStmt>(node))
	{
		out << "\n"; LVL(level - 1); out << "case ";
		EmitExpr(casestmt->GetExpr());
		out << ":";
		return;
	}
	else if (dyn_cast<const DefaultStmt>(node))
	{
		out << "\n"; LVL(level - 1); out << "default:";
		return;
	}
	else if (dyn_cast<const EmptyStmt>(node))
	{
		out << ";";
		return;
	}

	// For static analysis, generate a safe placeholder for unimplemented statements
	out << "\n"; LVL(level);
	out << "/* unknown_stmt */;";
}

void SLGenerator::GenerateStructs()
{
	// structs
	for (const ASTStructType* stc = ast.firstStructType; stc; stc = stc->nextStructType)
	{
		out << "struct " << stc->name << "\n{\n";
		for (const auto& m : stc->members)
		{
			LVL(1);
			EmitAccessPointDecl(m);
			out << ";\n";
		}
		out << "};\n\n";  // Add extra newline after struct
	}
}

void SLGenerator::GenerateFunctions()
{
	for (const ASTNode* fnn = ast.functionList.firstChild; fnn; fnn = fnn->next)
	{
		const ASTFunction* F = fnn->ToFunction();

		EmitTypeRef(F->GetReturnType());
		out << " " << F->name << "(";

		// Format function parameters with proper spacing
		bool first = true;
		for (ASTNode* arg = F->GetFirstArg(); arg; arg = arg->next)
		{
			if (!first) out << ", ";
			EmitVarDecl(arg->ToVarDecl());
			first = false;
		}
		out << ")";

		if (supportsSemantics && F->returnSemanticName.empty() == false)
		{
			out << " : " << F->returnSemanticName;
			if (F->returnSemanticIndex >= 0)
				out << F->returnSemanticIndex;
		}
		EmitStmt(F->GetCode(), 0);
		out << "\n\n";  // Add extra newline after function
	}
}


//////////////
//// HLSL ////
//////////////

void HLSLGenerator::EmitTypeRef(const ASTType* type)
{
	// For static analysis, handle NULL types gracefully
	if (!type)
	{
		// Add debug information to help identify the source of NULL types
		fprintf(stderr, "DEBUG: EmitTypeRef called with NULL type\n");
		out << "unknown_type";
		return;
	}

	switch (type->kind)
	{
	case ASTType::Void:        out << "void"; break;
	case ASTType::Bool:        out << "bool"; break;
	case ASTType::Int32:       out << "int"; break;
	case ASTType::UInt32:      out << "uint"; break;
	case ASTType::Float16:     out << "half"; break;
	case ASTType::Float32:     out << "float"; break;
	case ASTType::Sampler1D:   out << (IsGE4() ? "SAMPLER_1D" : "sampler1D"); break;
	case ASTType::Sampler2D:   out << (IsGE4() ? "SAMPLER_2D" : "sampler2D"); break;
	case ASTType::Sampler3D:   out << (IsGE4() ? "SAMPLER_3D" : "sampler3D"); break;
	case ASTType::SamplerCube: out << (IsGE4() ? "SAMPLER_CUBE" : "samplerCUBE"); break;
	case ASTType::Sampler1DCmp:   out << "SAMPLER_1D_CMP"; break;
	case ASTType::Sampler2DCmp:   out << "SAMPLER_2D_CMP"; break;
	case ASTType::SamplerCubeCmp: out << "SAMPLER_CUBE_CMP"; break;
	case ASTType::SamplerState: out << "SamplerState"; break;
	case ASTType::SamplerComparisonState: out << "SamplerComparisonState"; break;
	// Modern texture types
	case ASTType::Texture1D: out << "Texture1D"; break;
	case ASTType::Texture2D: out << "Texture2D"; break;
	case ASTType::Texture3D: out << "Texture3D"; break;
	case ASTType::TextureCube: out << "TextureCube"; break;
	case ASTType::Texture1DArray: out << "Texture1DArray"; break;
	case ASTType::Texture2DArray: out << "Texture2DArray"; break;
	case ASTType::TextureCubeArray: out << "TextureCubeArray"; break;
	case ASTType::Texture2DMS: out << "Texture2DMS"; break;
	case ASTType::Texture2DMSArray: out << "Texture2DMSArray"; break;
	// Buffer types
	case ASTType::Buffer: out << "Buffer"; break;
	case ASTType::StructuredBuffer: out << "StructuredBuffer"; break;
	case ASTType::RWBuffer: out << "RWBuffer"; break;
	case ASTType::RWStructuredBuffer: out << "RWStructuredBuffer"; break;
	case ASTType::AppendStructuredBuffer:
		out << "AppendStructuredBuffer";
		if (type->subType)
		{
			out << "<";
			EmitTypeRef(type->subType);
			out << ">";
		}
		break;
	case ASTType::ConsumeStructuredBuffer:
		out << "ConsumeStructuredBuffer";
		if (type->subType)
		{
			out << "<";
			EmitTypeRef(type->subType);
			out << ">";
		}
		break;
	case ASTType::ByteAddressBuffer: out << "ByteAddressBuffer"; break;
	case ASTType::RWByteAddressBuffer: out << "RWByteAddressBuffer"; break;
	case ASTType::ConstantBuffer:
		out << "ConstantBuffer";
		if (type->subType)
		{
			out << "<";
			EmitTypeRef(type->subType);
			out << ">";
		}
		break;
	// Read-write texture types
	case ASTType::RWTexture1D: out << "RWTexture1D"; break;
	case ASTType::RWTexture2D: out << "RWTexture2D"; break;
	case ASTType::RWTexture3D: out << "RWTexture3D"; break;
	case ASTType::RWTexture1DArray: out << "RWTexture1DArray"; break;
	case ASTType::RWTexture2DArray: out << "RWTexture2DArray"; break;
	case ASTType::Function:
		// Function types should not appear in normal type emission
		out << "/* function type */";
		break;
	case ASTType::Structure:
		out << type->ToStructType()->name;
		break;
	case ASTType::Vector:
		EmitTypeRef(type->subType);
		out << int(type->sizeX);
		break;
	case ASTType::Matrix:
		EmitTypeRef(type->subType);
		out << int(type->sizeX) << "x" << int(type->sizeY);
		break;
	case ASTType::Array:
		EmitTypeRef(type->subType);
		out << "[" << type->elementCount << "]";
		break;
	default:
		// Handle unknown types gracefully
		out << "/* unknown type " << (int)type->kind << " */";
		break;
	}
}

void HLSLGenerator::EmitExpr(const Expr* node)
{
	if (!node)
	{
		out << "null_expr";
		return;
	}

	if (auto* castexpr = dyn_cast<const CastExpr>(node))
	{
		// Only emit casts that were explicitly written in the source code
		if (castexpr->isExplicit)
		{
			out << "(";
			EmitTypeRef(castexpr->GetReturnType());
			out << ")";
		}
		EmitExpr(castexpr->GetSource());
		return;
	}
	else if (auto* ile = dyn_cast<const InitListExpr>(node))
	{
		if (ile->GetReturnType()->kind == ASTType::Array ||
			ile->GetReturnType()->kind == ASTType::Structure)
		{
			out << "{";
			for (ASTNode* ch = ile->firstChild; ch; ch = ch->next)
			{
				if (ch->prev)
					out << ", ";
				if (ch->ToExpr())
				{
					EmitExpr(ch->ToExpr());
				}
				else
				{
					out << "/* ERROR: Invalid initializer element */";
				}
			}
			out << "}";
		}
		else
		{
			EmitTypeRef(ile->GetReturnType());
			out << "(";
			for (ASTNode* ch = ile->firstChild; ch; ch = ch->next)
			{
				if (ch->prev)
					out << ", ";
				if (ch->ToExpr())
				{
					EmitExpr(ch->ToExpr());
				}
				else
				{
					out << "/* ERROR: Invalid initializer element */";
				}
			}
			out << ")";
		}
		return;
	}
	else if (auto* dre = dyn_cast<const DeclRefExpr>(node))
	{
		if (IsGE4() &&
			dre->decl &&
			(dre->decl->flags & VarDecl::ATTR_Global) &&
			(dre->decl->GetType()->IsSampler() || dre->decl->GetType()->IsTexture()))
		{
			if (IsGE6())
			{
				// Modern HLSL: Use direct variable name (will be handled in texture sampling)
				out << dre->decl->name;
			}
			else
			{
				// Legacy HLSL: Use wrapper function for traditional samplers, direct name for modern textures
				if (dre->decl->GetType()->IsSampler())
				{
					out << "GET_" << dre->decl->name << "()";
				}
				else
				{
					// Modern texture types - use direct name
					out << dre->decl->name;
				}
			}
			return;
		}
	}
	else if (auto* op = dyn_cast<const OpExpr>(node))
	{
		// Define texture sampling type for all HLSL versions
		enum Type
		{
			Normal,
			Bias,
			Grad,
			Level,
			Cmp,
			CmpLevelZero,
		};
		Type type = Normal; // Default to normal sampling

		if (IsGE4())
		{
			switch (op->opKind)
			{
			case Op_Tex1D:       type = Normal;   goto texSample;
			case Op_Tex1DBias:   type = Bias;     goto texSample;
			case Op_Tex1DGrad:   type = Grad;     goto texSample;
			case Op_Tex1DLOD:    type = Level;    goto texSample;
			case Op_Tex1DProj:   assert( false ); return;
			case Op_Tex2D:       type = Normal;   goto texSample;
			case Op_Tex2DBias:   type = Bias;     goto texSample;
			case Op_Tex2DGrad:   type = Grad;     goto texSample;
			case Op_Tex2DLOD:    type = Level;    goto texSample;
			case Op_Tex2DProj:   assert( false ); return;
			case Op_Tex3D:       type = Normal;   goto texSample;
			case Op_Tex3DBias:   type = Bias;     goto texSample;
			case Op_Tex3DGrad:   type = Grad;     goto texSample;
			case Op_Tex3DLOD:    type = Level;    goto texSample;
			case Op_Tex3DProj:   assert( false ); return;
			case Op_TexCube:     type = Normal;   goto texSample;
			case Op_TexCubeBias: type = Bias;     goto texSample;
			case Op_TexCubeGrad: type = Grad;     goto texSample;
			case Op_TexCubeLOD:  type = Level;    goto texSample;
			case Op_TexCubeProj: assert( false ); return;

			case Op_Tex1DCmp:
			case Op_Tex2DCmp:
			case Op_TexCubeCmp: type = Cmp; goto texSample;
			case Op_Tex1DLOD0Cmp:
			case Op_Tex2DLOD0Cmp:
			case Op_TexCubeLOD0Cmp: type = CmpLevelZero; goto texSample;
			default: break;
			texSample:
				// Always use modern .Sample() syntax for better readability
				if (!op->firstChild || !op->firstChild->ToExpr())
				{
					// For static optimization, generate a placeholder function call
					out << "tex2D";
					goto texSampleArgs;
				}
				EmitExpr(op->firstChild->ToExpr());
				out << ".";
				switch (type)
				{
				case Normal: out << "Sample";      break;
				case Bias:   out << "SampleBias";  break;
				case Grad:   out << "SampleGrad";  break;
				case Level:  out << "SampleLevel"; break;
				case Cmp:    out << "SampleCmp";   break;
				case CmpLevelZero: out << "SampleCmpLevelZero"; break;
				}
			texSampleArgs:
				out << "(";
				// For static optimization, emit all available parameters
				bool first = true;
				ASTNode* startNode = op->firstChild;

				// If we used the placeholder path, start from the first child
				// Otherwise, skip the texture (first child) and start from sampler (second child)
				if (op->firstChild && op->firstChild->ToExpr())
				{
					startNode = op->firstChild->next; // Skip texture, start from sampler
				}

				for (ASTNode* ch = startNode; ch; ch = ch->next)
				{
					if (!first) out << ", ";
					if (ch && ch->ToExpr())
					{
						EmitExpr(ch->ToExpr());
					}
					else
					{
						out << "float4(0,0,0,1)"; // Placeholder for missing parameters
					}
					first = false;
				}
				out << ")";
				return;
			}
		}
		const char* fnstr = nullptr;
		const char* opstr = ",";
		switch (op->opKind)
		{
		case Op_ATan2:     fnstr = "atan2";     break;
		case Op_Clip:      fnstr = "clip";      break;
		case Op_Determinant: fnstr = "determinant"; break;
		case Op_DDX:       fnstr = "ddx";       break;
		case Op_DDY:       fnstr = "ddy";       break;
		case Op_FMod:      fnstr = "fmod";      break;
		case Op_Frac:      fnstr = "frac";      break;
		case Op_LdExp:     fnstr = "ldexp";     break;
		case Op_Lerp:      fnstr = "lerp";      break;
		case Op_MulMM:
		case Op_MulMV:
		case Op_MulVM:     fnstr = "mul";       break;
		case Op_RSqrt:     fnstr = "rsqrt";     break;
		case Op_Saturate:  fnstr = "saturate";  break;
		// Texture sampling operations - use modern .Sample() syntax
		case Op_Tex1D:       type = Normal; goto texSample;
		case Op_Tex1DBias:   type = Bias;   goto texSample;
		case Op_Tex1DGrad:   type = Grad;   goto texSample;
		case Op_Tex1DLOD:    type = Level;  goto texSample;
		case Op_Tex1DProj:   type = Normal; goto texSample;
		case Op_Tex2D:       type = Normal; goto texSample;
		case Op_Tex2DBias:   type = Bias;   goto texSample;
		case Op_Tex2DGrad:   type = Grad;   goto texSample;
		case Op_Tex2DLOD:    type = Level;  goto texSample;
		case Op_Tex2DProj:   type = Normal; goto texSample;
		case Op_Tex3D:       type = Normal; goto texSample;
		case Op_Tex3DBias:   type = Bias;   goto texSample;
		case Op_Tex3DGrad:   type = Grad;   goto texSample;
		case Op_Tex3DLOD:    type = Level;  goto texSample;
		case Op_Tex3DProj: fnstr = "tex3Dproj"; break;
		case Op_TexCube:     fnstr = "texCUBE";     break;
		case Op_TexCubeBias: fnstr = "texCUBEbias"; break;
		case Op_TexCubeGrad: fnstr = "texCUBEgrad"; break;
		case Op_TexCubeLOD:  fnstr = "texCUBElod";  break;
		case Op_TexCubeProj: fnstr = "texCUBEproj"; break;
		}
		if (fnstr)
		{
			out << fnstr << "(";

			// Special handling for texture sampling functions in SM3
			// Skip the sampler parameter (second child) for tex2D, tex3D, etc.
			bool isTextureSample = (op->opKind == Op_Tex1D || op->opKind == Op_Tex2D || op->opKind == Op_Tex3D ||
			                       op->opKind == Op_TexCube || op->opKind == Op_Tex1DBias || op->opKind == Op_Tex2DBias ||
			                       op->opKind == Op_Tex3DBias || op->opKind == Op_TexCubeBias || op->opKind == Op_Tex1DGrad ||
			                       op->opKind == Op_Tex2DGrad || op->opKind == Op_Tex3DGrad || op->opKind == Op_TexCubeGrad ||
			                       op->opKind == Op_Tex1DLOD || op->opKind == Op_Tex2DLOD || op->opKind == Op_Tex3DLOD ||
			                       op->opKind == Op_TexCubeLOD || op->opKind == Op_Tex1DProj || op->opKind == Op_Tex2DProj ||
			                       op->opKind == Op_Tex3DProj || op->opKind == Op_TexCubeProj);

			if (isTextureSample)
			{
				// For texture sampling: emit texture (first child), skip sampler (second child), emit coordinates (remaining children)
				bool first = true;
				int childIndex = 0;
				for (ASTNode* ch = op->firstChild; ch; ch = ch->next, childIndex++)
				{
					if (childIndex == 1) continue; // Skip sampler parameter

					if (!first) out << ", ";
					EmitExpr(ch->ToExpr());
					first = false;
				}
			}
			else
			{
				// Normal function call: emit all parameters
				for (ASTNode* ch = op->firstChild; ch; ch = ch->next)
				{
					EmitExpr(ch->ToExpr());
					if (ch->next)
						out << ", ";
				}
			}
			out << ")";
			return;
		}
	}

	SLGenerator::EmitExpr(node);
}

void HLSLGenerator::GenerateFunctions()
{
	for (const ASTNode* fnn = ast.functionList.firstChild; fnn; fnn = fnn->next)
	{
		const ASTFunction* F = fnn->ToFunction();

		// Add [numthreads] attribute for compute shader entry points
		if (F == ast.entryPoint && IsGE6())
		{
			// Check if this is a compute shader by looking at function parameters
			// Compute shaders typically have SV_DispatchThreadID or similar semantics
			bool isComputeShader = false;
			for (ASTNode* arg = F->GetFirstArg(); arg; arg = arg->next)
			{
				auto* argvd = arg->ToVarDecl();
				if (argvd->semanticName == "SV_DispatchThreadID" ||
					argvd->semanticName == "SV_GroupThreadID" ||
					argvd->semanticName == "SV_GroupID")
				{
					isComputeShader = true;
					break;
				}
			}

			if (isComputeShader)
			{
				out << "[numthreads(8, 8, 1)]\n";
			}
		}

		EmitTypeRef(F->GetReturnType());
		out << " " << F->name << "(";

		// Format function parameters with proper spacing
		bool first = true;
		for (ASTNode* arg = F->GetFirstArg(); arg; arg = arg->next)
		{
			if (!first) out << ", ";
			EmitVarDecl(arg->ToVarDecl());
			first = false;
		}
		out << ")";

		if (supportsSemantics && F->returnSemanticName.empty() == false)
		{
			out << " : " << F->returnSemanticName;
			if (F->returnSemanticIndex >= 0)
				out << F->returnSemanticIndex;
		}
		EmitStmt(F->GetCode(), 0);
		out << "\n\n";  // Add extra newline after function
	}
}

void HLSLGenerator::Generate()
{
	if (shaderFormat == OSF_HLSL_SM4)
	{
		out <<
			"struct SAMPLER_1D { Texture1D tex; SamplerState smp; };\n"
			"struct SAMPLER_2D { Texture2D tex; SamplerState smp; };\n"
			"struct SAMPLER_3D { Texture3D tex; SamplerState smp; };\n"
			"struct SAMPLER_Cube { TextureCube tex; SamplerState smp; };\n"
			"struct SAMPLER_1D_CMP { Texture1D tex; SamplerComparisonState smp; };\n"
			"struct SAMPLER_2D_CMP { Texture2D tex; SamplerComparisonState smp; };\n"
			"struct SAMPLER_Cube_CMP { TextureCube tex; SamplerComparisonState smp; };\n"
		;
	}

	SLGenerator::GenerateStructs();

	for (ASTNode* g = ast.globalVars.firstChild; g; g = g->next)
	{
		if (auto* cbuf = dyn_cast<CBufferDecl>(g))
		{
			if (IsGE6())
			{
				out << "cbuffer " << cbuf->name;
				if (cbuf->bufRegID >= 0)
				{
					out << " : register(b" << cbuf->bufRegID << ")";
				}
				out << "\n{\n";
				for (ASTNode* cbv = cbuf->firstChild; cbv; cbv = cbv->next)
				{
					LVL(1);
					auto* vd = cbv->ToVarDecl();
					// In modern mode, don't emit 'uniform' keyword inside cbuffer
					bool wasUniform = vd->flags & VarDecl::ATTR_Uniform;
					if (wasUniform) vd->flags &= ~VarDecl::ATTR_Uniform;
					EmitVarDecl(vd);
					if (wasUniform) vd->flags |= VarDecl::ATTR_Uniform;
					out << ";\n";
				}
				out << "}\n\n";  // Add extra newline after cbuffer
			}
			else
			{
				out << "cbuffer " << cbuf->name;
				if (cbuf->bufRegID >= 0)
				{
					out << " : register(b" << cbuf->bufRegID << ")";
				}
				out << "\n{\n";
				for (ASTNode* cbv = cbuf->firstChild; cbv; cbv = cbv->next)
				{
					LVL(1);
					EmitVarDecl(cbv->ToVarDecl());
					out << ";\n";
				}
				out << "}\n\n";  // Add extra newline after cbuffer
			}
		}
		else
		{
			auto* vd = g->ToVarDecl();
			if (vd->flags & VarDecl::ATTR_Hidden)
				continue;
			bool isRWTexture = (vd->GetType()->kind == ASTType::RWTexture1D || vd->GetType()->kind == ASTType::RWTexture2D ||
				vd->GetType()->kind == ASTType::RWTexture3D || vd->GetType()->kind == ASTType::RWTexture1DArray ||
				vd->GetType()->kind == ASTType::RWTexture2DArray);

			if (IsGE4() && (vd->GetType()->IsSampler() || vd->GetType()->IsTexture() || vd->GetType()->IsBuffer() || isRWTexture))
			{
				if (IsGE6())
				{
					// Modern HLSL: Use native texture and sampler declarations
					const char* texType = nullptr;
					bool smpComp = false;
					bool isModernTexture = false;

					// Handle traditional sampler types
					switch (vd->GetType()->kind)
					{
					case ASTType::Sampler1D:   texType = "Texture1D";   break;
					case ASTType::Sampler2D:   texType = "Texture2D";   break;
					case ASTType::Sampler3D:   texType = "Texture3D";   break;
					case ASTType::SamplerCube: texType = "TextureCube"; break;
					case ASTType::Sampler1DCmp:   texType = "Texture1D";   smpComp = true; break;
					case ASTType::Sampler2DCmp:   texType = "Texture2D";   smpComp = true; break;
					case ASTType::SamplerCubeCmp: texType = "TextureCube"; smpComp = true; break;
					case ASTType::SamplerState: texType = "SamplerState"; isModernTexture = true; break;
					case ASTType::SamplerComparisonState: texType = "SamplerComparisonState"; isModernTexture = true; break;
					// Handle modern texture types
					case ASTType::Texture1D:   texType = "Texture1D";   isModernTexture = true; break;
					case ASTType::Texture2D:   texType = "Texture2D";   isModernTexture = true; break;
					case ASTType::Texture3D:   texType = "Texture3D";   isModernTexture = true; break;
					case ASTType::TextureCube: texType = "TextureCube"; isModernTexture = true; break;
					case ASTType::Texture1DArray: texType = "Texture1DArray"; isModernTexture = true; break;
					case ASTType::Texture2DArray: texType = "Texture2DArray"; isModernTexture = true; break;
					case ASTType::TextureCubeArray: texType = "TextureCubeArray"; isModernTexture = true; break;
					case ASTType::Texture2DMS: texType = "Texture2DMS"; isModernTexture = true; break;
					case ASTType::Texture2DMSArray: texType = "Texture2DMSArray"; isModernTexture = true; break;
					// Handle buffer types
					case ASTType::Buffer: texType = "Buffer"; isModernTexture = true; break;
					case ASTType::StructuredBuffer: texType = "StructuredBuffer"; isModernTexture = true; break;
					case ASTType::RWBuffer: texType = "RWBuffer"; isModernTexture = true; break;
					case ASTType::RWStructuredBuffer: texType = "RWStructuredBuffer"; isModernTexture = true; break;
					case ASTType::AppendStructuredBuffer: texType = "AppendStructuredBuffer"; isModernTexture = true; break;
					case ASTType::ConsumeStructuredBuffer: texType = "ConsumeStructuredBuffer"; isModernTexture = true; break;
					case ASTType::ByteAddressBuffer: texType = "ByteAddressBuffer"; isModernTexture = true; break;
					case ASTType::RWByteAddressBuffer: texType = "RWByteAddressBuffer"; isModernTexture = true; break;
					// Handle read-write texture types
					case ASTType::RWTexture1D: texType = "RWTexture1D"; isModernTexture = true; break;
					case ASTType::RWTexture2D: texType = "RWTexture2D"; isModernTexture = true; break;
					case ASTType::RWTexture3D: texType = "RWTexture3D"; isModernTexture = true; break;
					case ASTType::RWTexture1DArray: texType = "RWTexture1DArray"; isModernTexture = true; break;
					case ASTType::RWTexture2DArray: texType = "RWTexture2DArray"; isModernTexture = true; break;
					default:
						// For static analysis, handle unknown texture/sampler types
						texType = "UnknownTexture";
						isModernTexture = true;
						break;
					}

					if (isModernTexture)
					{
						// For modern texture types, emit the texture declaration with template parameters
						out << texType;

						// Add template parameter for typed textures
						if (vd->GetType()->subType)
						{
							if (!vd->GetType()->IsSampler())
							{
								out << "<";
								EmitTypeRef(vd->GetType()->subType);
								out << ">";
							}
						}

						out << " " << vd->name;

						if (vd->regID >= 0)
						{
							// Determine correct register type
							char regType = 't'; // Default for SRV
							if (strncmp(texType, "RW", 2) == 0 || // RWTexture*, RWBuffer*, etc.
								strcmp(texType, "AppendStructuredBuffer") == 0 ||
								strcmp(texType, "ConsumeStructuredBuffer") == 0)
								regType = 'u'; // UAV register
							else if (vd->GetType()->IsSampler())
								regType = 's'; // Sampler register

							out << " : register(" << regType << vd->regID << ")";
						}
						out << ";\n";
					}
					else
					{
						// For traditional sampler types, generate both texture and sampler
						// Generate modern texture declaration
						out << texType << " " << vd->name << "_Texture";
						if (vd->regID >= 0)
							out << " : register(" << vd->regType << vd->regID << ")";
						out << ";\n";

						// Generate modern sampler declaration
						out << (smpComp ? "SamplerComparisonState" : "SamplerState");
						out << " " << vd->name << "_Sampler";
						if (vd->regID >= 0)
							out << " : register(s" << vd->regID << ")";
						out << ";\n";
					}
				}
				else
				{
					// Legacy HLSL: Use wrapper structures
					const char* sfx = nullptr;
					bool smpComp = false;
					bool isModernTexture = false;

					// Handle traditional sampler types
					switch (vd->GetType()->kind)
					{
					case ASTType::Sampler1D:   sfx = "1D";   break;
					case ASTType::Sampler2D:   sfx = "2D";   break;
					case ASTType::Sampler3D:   sfx = "3D";   break;
					case ASTType::SamplerCube: sfx = "Cube"; break;
					case ASTType::Sampler1DCmp:   sfx = "1D";   smpComp = true; break;
					case ASTType::Sampler2DCmp:   sfx = "2D";   smpComp = true; break;
					case ASTType::SamplerCubeCmp: sfx = "Cube"; smpComp = true; break;
					// Handle modern texture types - map to equivalent traditional types
					case ASTType::Texture1D:   sfx = "1D";   isModernTexture = true; break;
					case ASTType::Texture2D:   sfx = "2D";   isModernTexture = true; break;
					case ASTType::Texture3D:   sfx = "3D";   isModernTexture = true; break;
					case ASTType::TextureCube: sfx = "Cube"; isModernTexture = true; break;
					case ASTType::Texture1DArray: sfx = "1D"; isModernTexture = true; break;
					case ASTType::Texture2DArray: sfx = "2D"; isModernTexture = true; break;
					case ASTType::TextureCubeArray: sfx = "Cube"; isModernTexture = true; break;
					case ASTType::Texture2DMS: sfx = "2D"; isModernTexture = true; break;
					case ASTType::Texture2DMSArray: sfx = "2D"; isModernTexture = true; break;
					}

					if (isModernTexture)
					{
						// For modern texture types, we need a separate sampler
						// This is a simplified approach - in reality we'd need to track sampler associations
						out << "Texture" << sfx << " " << vd->name;
						if (vd->regID >= 0)
							out << " : register(" << vd->regType << vd->regID << ")";
						out << ";\n";
					}
					else
					{
						// Traditional sampler handling
						const char* smpSfx = smpComp ? "_CMP" : "";
						out << "Texture" << sfx << " TEX_" << vd->name;
						if (vd->regID >= 0)
							out << " : register(" << vd->regType << vd->regID << ")";
						out << ";\n";

						out << (smpComp ? "SamplerComparisonState" : "SamplerState");
						out << " SMP_" << vd->name;
						if (vd->regID >= 0)
							out << " : register(s" << vd->regID << ")";
						out << ";\n";

						out << "SAMPLER_" << sfx << smpSfx << " GET_" << vd->name
							<< "(){ SAMPLER_" << sfx << smpSfx << " v = { TEX_" << vd->name
							<< ", SMP_" << vd->name << " }; return v; }\n";
					}
				}
			}
			else
			{
				EmitVarDecl(vd);
				out << ";\n";
			}
		}
	}

	GenerateFunctions();
}


//////////////
//// GLSL ////
//////////////

void GLSLGenerator::EmitTypeRef(const ASTType* type)
{
	// For static analysis, handle NULL types gracefully
	if (!type)
	{
		out << "unknown_type";
		return;
	}

	switch (type->kind)
	{
	case ASTType::Void: out << "void"; break;
	case ASTType::Bool: out << "bool"; break;
	case ASTType::Int32: out << "int"; break;
	case ASTType::UInt32: out << "uint"; break;
	case ASTType::Float16:
		if (IsES())
			out << "mediump float";
		else
			out << "float"; // Desktop GLSL doesn't have explicit half precision
		break;
	case ASTType::Float32: out << "float"; break;

	// Traditional sampler types
	case ASTType::Sampler1D: out << "sampler1D"; break;
	case ASTType::Sampler2D: out << "sampler2D"; break;
	case ASTType::Sampler3D: out << "sampler3D"; break;
	case ASTType::SamplerCube: out << "samplerCube"; break;
	case ASTType::Sampler1DCmp: out << "sampler1DShadow"; break;
	case ASTType::Sampler2DCmp: out << "sampler2DShadow"; break;
	case ASTType::SamplerCubeCmp: out << "samplerCubeShadow"; break;

	// Modern texture types (map to equivalent GLSL samplers)
	case ASTType::Texture1D: out << "sampler1D"; break;
	case ASTType::Texture2D: out << "sampler2D"; break;
	case ASTType::Texture3D: out << "sampler3D"; break;
	case ASTType::TextureCube: out << "samplerCube"; break;
	case ASTType::Texture1DArray:
		if (IsGE330())
			out << "sampler1DArray";
		else
			out << "sampler2D"; // Fallback for older versions
		break;
	case ASTType::Texture2DArray:
		if (IsGE330())
			out << "sampler2DArray";
		else
			out << "sampler2D"; // Fallback for older versions
		break;
	case ASTType::TextureCubeArray:
		if (IsGE400())
			out << "samplerCubeArray";
		else
			out << "samplerCube"; // Fallback for older versions
		break;
	case ASTType::Texture2DMS:
		if (IsGE330())
			out << "sampler2DMS";
		else
			out << "sampler2D"; // Fallback for older versions
		break;
	case ASTType::Texture2DMSArray:
		if (IsGE330())
			out << "sampler2DMSArray";
		else
			out << "sampler2D"; // Fallback for older versions
		break;

	// Modern sampler states (GLSL doesn't have separate sampler state)
	case ASTType::SamplerState: out << "sampler2D"; break;
	case ASTType::SamplerComparisonState: out << "sampler2DShadow"; break;

	case ASTType::Structure:
		out << type->ToStructType()->name;
		break;
	case ASTType::Vector:
		switch (type->subType->kind)
		{
		case ASTType::Bool: out << "bvec"; break;
		case ASTType::Int32: out << "ivec"; break;
		case ASTType::UInt32: out << "uvec"; break;
		case ASTType::Float16: out << "mediump vec"; break;
		case ASTType::Float32: out << "vec"; break;
		}
		out << int(type->sizeX);
		break;
	case ASTType::Matrix:
		switch (type->subType->kind)
		{
		case ASTType::Bool: out << "mat"; break;
		case ASTType::Int32: out << "mat"; break;
		case ASTType::UInt32: out << "mat"; break;
		case ASTType::Float16: out << "mediump mat"; break;
		case ASTType::Float32: out << "mat"; break;
		}
		out << int(type->sizeX);
		if (type->sizeX != type->sizeY)
			out << "x" << int(type->sizeY);
		break;
	case ASTType::Array:
		EmitTypeRef(type->subType);
		out << "[" << type->elementCount << "]";
		break;
	}
}

void GLSLGenerator::EmitExpr(const Expr* node)
{
	if (auto* castexpr = dyn_cast<const CastExpr>(node))
	{
		EmitTypeRef(castexpr->GetReturnType());
		out << "(";
		EmitExpr(castexpr->GetSource());
		out << ")";
		return;
	}
	else if (auto* ile = dyn_cast<const InitListExpr>(node))
	{
		EmitTypeRef(ile->GetReturnType());
		out << "(";
		for (ASTNode* ch = ile->firstChild; ch; ch = ch->next)
		{
			if (ch->prev)
				out << ", ";
			EmitExpr(ch->ToExpr());
		}
		out << ")";
		return;
	}
	else if (auto* op = dyn_cast<const OpExpr>(node))
	{
		const char* fnstr = nullptr;
		const char* opstr = ",";
		const char* fnEnd = ")";
		switch (op->opKind)
		{
		case Op_Multiply:
			if (op->GetLft()->GetReturnType()->kind == ASTType::Matrix &&
				op->GetRgt()->GetReturnType()->kind == ASTType::Matrix)
				fnstr = "matrixCompMult";
			break;
		case Op_Modulus:
			if (op->GetLft()->GetReturnType()->IsFloatBased() ||
				op->GetRgt()->GetReturnType()->IsFloatBased())
				fnstr = "mod";
			break;
		case Op_ATan2:     fnstr = "atan";  break;
		case Op_Clip:      assert( false ); break;
		case Op_DDX:       fnstr = "dFdx";  break;
		case Op_DDY:       fnstr = "dFdy";  break;
		case Op_FMod:      assert( false ); break;
		case Op_Frac:      fnstr = "fract"; break;
		case Op_LdExp:
			out << "(";
			EmitExpr(op->GetFirstArg()->ToExpr());
			out << "*exp2(";
			EmitExpr(op->GetFirstArg()->next->ToExpr());
			out << "))";
			return;
		case Op_Lerp:      fnstr = "mix";   break;
		case Op_ModGLSL:   fnstr = "mod";   break;
		case Op_MulMM:
		case Op_MulMV:
		case Op_MulVM:     fnstr = ""; opstr = "*"; break;
		case Op_Round:     assert( version >= 130 ); break;
		case Op_RSqrt:     fnstr = "inversesqrt"; break;
		case Op_Saturate:
			out << "clamp(";
			EmitExpr(op->GetFirstArg()->ToExpr());
			out << ",0.0,1.0)";
			return;
		case Op_Tex1D:     fnstr = version < 140 ? "UNAVAILABLE"      : "texture";     break;
		case Op_Tex1DBias: fnstr = version < 140 ? "UNAVAILABLE"      : "texture";     break;
		case Op_Tex1DGrad: fnstr = version < 140 ? "UNAVAILABLE"      : "textureGrad"; break;
		case Op_Tex1DLOD:  fnstr = version < 140 ? "UNAVAILABLE"      : "textureLod";  break;
		case Op_Tex1DProj: fnstr = version < 140 ? "UNAVAILABLE"      : "textureProj"; break;
		case Op_Tex2D:     fnstr = version < 140 ? "texture2D"        : "texture";     break;
		case Op_Tex2DBias: fnstr = version < 140 ? "texture2D"        : "texture";     break;
		case Op_Tex2DGrad: fnstr = version < 140 ? "texture2DGradEXT" : "textureGrad"; break;
		case Op_Tex2DLOD:  fnstr = version < 140 ?
			(ast.stage == ShaderStage_Pixel ? "texture2DLodEXT" : "texture2DLod") :
			"textureLod";  break;
		case Op_Tex2DProj: fnstr = version < 140 ? "texture2DProj"    : "textureProj"; break;
		case Op_Tex3D:     fnstr = version < 140 ? "UNAVAILABLE"      : "texture";     break;
		case Op_Tex3DBias: fnstr = version < 140 ? "UNAVAILABLE"      : "texture";     break;
		case Op_Tex3DGrad: fnstr = version < 140 ? "UNAVAILABLE"      : "textureGrad"; break;
		case Op_Tex3DLOD:  fnstr = version < 140 ? "UNAVAILABLE"      : "textureLod";  break;
		case Op_Tex3DProj: fnstr = version < 140 ? "UNAVAILABLE"      : "textureProj"; break;
		case Op_TexCube:     fnstr = version < 140 ? "textureCube"        : "texture";     break;
		case Op_TexCubeBias: fnstr = version < 140 ? "textureCube"        : "texture";     break;
		case Op_TexCubeGrad: fnstr = version < 140 ? "textureCubeGradEXT" : "textureGrad"; break;
		case Op_TexCubeLOD:  fnstr = version < 140 ?
			(ast.stage == ShaderStage_Pixel ? "textureCubeLodEXT" : "textureCubeLod") :
			"textureLod";  break;
		case Op_TexCubeProj: fnstr = version < 140 ? "textureCubeProj"    : "textureProj"; break;
		case Op_Tex1DLOD0Cmp:
		case Op_Tex2DLOD0Cmp:
		case Op_TexCubeLOD0Cmp: fnEnd = ",-32)"; // <- bias LOD away from lower mip levels
			// passthrough
		case Op_Tex1DCmp:
		case Op_Tex2DCmp:
		case Op_TexCubeCmp: fnstr = version < 140 ? "UNAVAILABLE" : "texture"; break;
		case Op_Trunc:     assert(version >= 130); break;
		}
		if (fnstr)
		{
			out << fnstr << "(";
			for (ASTNode* ch = op->firstChild; ch; ch = ch->next)
			{
				EmitExpr(ch->ToExpr());
				if (ch->next)
					out << opstr;
			}
			out << fnEnd; // normally )
			return;
		}
	}
	else if (auto* binop = dyn_cast<const BinaryOpExpr>(node))
	{
		if (binop->GetLft()->GetReturnType()->kind == ASTType::Vector ||
			binop->GetLft()->GetReturnType()->kind == ASTType::Vector)
		{
			const char* opstr = nullptr;
			switch (binop->opType)
			{
			case STT_OP_Eq: opstr = "equal"; goto vcmp;
			case STT_OP_NEq: opstr = "notEqual"; goto vcmp;
			case STT_OP_Less: opstr = "lessThan"; goto vcmp;
			case STT_OP_LEq: opstr = "lessThanEqual"; goto vcmp;
			case STT_OP_Greater: opstr = "greaterThan"; goto vcmp;
			case STT_OP_GEq: opstr = "greaterThanEqual"; goto vcmp;
			default: break;
			vcmp:
				out << opstr << "(";
				EmitExpr(binop->GetLft());
				out << ",";
				EmitExpr(binop->GetRgt());
				out << ")";
				return;
			}
		}
	}

	SLGenerator::EmitExpr(node);
}

void GLSLGenerator::Generate()
{
	// Emit version directive
	if (IsES())
	{
		out << "#version " << version << " es\n";
		if (version >= 300)
		{
			out << "precision highp float;\n";
			out << "precision highp int;\n";
		}
		else
		{
			out << "precision highp float;\n";
		}

		// ES-specific extensions
		if (version == 100)
		{
			if (ast.usingDerivatives)
			{
				out << "#extension GL_OES_standard_derivatives : enable\n";
			}
			if ((ast.usingLODTextureSampling && ast.stage == ShaderStage_Pixel) ||
				ast.usingGradTextureSampling)
			{
				out << "#extension GL_EXT_shader_texture_lod : enable\n";
			}
		}
		else if (version >= 300)
		{
			// ES 3.0+ extensions
			out << "#ifdef GL_EXT_texture_array\n";
			out << "#extension GL_EXT_texture_array : enable\n";
			out << "#endif\n";
		}
	}
	else
	{
		out << "#version " << version << "\n";

		// Desktop GLSL extensions
		if (version >= 330)
		{
			out << "#ifdef GL_ARB_separate_shader_objects\n";
			out << "#extension GL_ARB_separate_shader_objects : enable\n";
			out << "#endif\n";
			out << "#ifdef GL_ARB_explicit_attrib_location\n";
			out << "#extension GL_ARB_explicit_attrib_location : enable\n";
			out << "#endif\n";
		}

		if (version >= 400)
		{
			out << "#ifdef GL_ARB_texture_cube_map_array\n";
			out << "#extension GL_ARB_texture_cube_map_array : enable\n";
			out << "#endif\n";
		}
	}

	out << "\n";

	SLGenerator::GenerateStructs();

	for (ASTNode* g = ast.globalVars.firstChild; g; g = g->next)
	{
		if (auto* cbuf = dyn_cast<CBufferDecl>(g))
		{
			if (version >= 140)
			{
				out << "layout(std140) uniform " << cbuf->name;
				out << "\n{\n";
			}
			for (ASTNode* cbv = cbuf->firstChild; cbv; cbv = cbv->next)
			{
				LVL(1);
				EmitVarDecl(cbv->ToVarDecl());
				out << ";\n";
			}
			if (version >= 140)
			{
				out << "};\n";
			}
		}
		else
		{
			auto* gv = g->ToVarDecl();
			if (gv->flags & VarDecl::ATTR_Hidden)
				continue;
			EmitVarDecl(gv);
			out << ";\n";
		}
	}

	SLGenerator::GenerateFunctions();
}


void HOC::GenerateHLSL_SM3(const AST& ast, OutStream& out)
{
	HLSLGenerator gen(ast, out);
	gen.Generate();
}

void HOC::GenerateHLSL_SM4(const AST& ast, OutStream& out)
{
	HLSLGenerator gen(ast, out);
	gen.shaderFormat = OSF_HLSL_SM4;
	gen.Generate();
}

void HOC::GenerateHLSL_SM5(const AST& ast, OutStream& out)
{
	HLSLGenerator gen(ast, out);
	gen.shaderFormat = OSF_HLSL_SM5;
	gen.Generate();
}

void HOC::GenerateHLSL_SM6(const AST& ast, OutStream& out)
{
	HLSLGenerator gen(ast, out);
	gen.shaderFormat = OSF_HLSL_SM6;
	gen.Generate();
}


void HOC::GenerateGLSL_140(const AST& ast, OutStream& out)
{
	GLSLGenerator gen(ast, out);
	gen.version = 140;
	gen.shaderFormat = OSF_GLSL_140;
	gen.Generate();
}

void HOC::GenerateGLSL_330(const AST& ast, OutStream& out)
{
	GLSLGenerator gen(ast, out);
	gen.version = 330;
	gen.shaderFormat = OSF_GLSL_330;
	gen.supportsDoubles = true;
	gen.Generate();
}

void HOC::GenerateGLSL_450(const AST& ast, OutStream& out)
{
	GLSLGenerator gen(ast, out);
	gen.version = 450;
	gen.shaderFormat = OSF_GLSL_450;
	gen.supportsDoubles = true;
	gen.Generate();
}

void HOC::GenerateGLSL_ES_100(const AST& ast, OutStream& out)
{
	GLSLGenerator gen(ast, out);
	gen.version = 100;
	gen.shaderFormat = OSF_GLSL_ES_100;
	gen.supportsStageInOut = false;
	gen.Generate();
}

void HOC::GenerateGLSL_ES_300(const AST& ast, OutStream& out)
{
	GLSLGenerator gen(ast, out);
	gen.version = 300;
	gen.shaderFormat = OSF_GLSL_ES_300;
	gen.supportsDoubles = false; // ES doesn't support doubles
	gen.supportsStageInOut = true;
	gen.Generate();
}

void HOC::GenerateGLSL_ES_320(const AST& ast, OutStream& out)
{
	GLSLGenerator gen(ast, out);
	gen.version = 320;
	gen.shaderFormat = OSF_GLSL_ES_320;
	gen.supportsDoubles = false; // ES doesn't support doubles
	gen.supportsStageInOut = true;
	gen.Generate();
}

// Implementation of operator precedence function
int SLGenerator::GetOperatorPrecedence(SLTokenType opType)
{
	switch (opType)
	{
	// Highest precedence
	case STT_OP_Mul:
	case STT_OP_Div:
	case STT_OP_Mod:
		return 10;

	case STT_OP_Add:
	case STT_OP_Sub:
		return 9;

	case STT_OP_Lsh:
	case STT_OP_Rsh:
		return 8;

	case STT_OP_Less:
	case STT_OP_LEq:
	case STT_OP_Greater:
	case STT_OP_GEq:
		return 7;

	case STT_OP_Eq:
	case STT_OP_NEq:
		return 6;

	case STT_OP_And:
		return 5;

	case STT_OP_Xor:
		return 4;

	case STT_OP_Or:
		return 3;

	case STT_OP_LogicalAnd:
		return 2;

	case STT_OP_LogicalOr:
		return 1;

	// Assignment operators (lowest precedence)
	case STT_OP_Assign:
	case STT_OP_AddEq:
	case STT_OP_SubEq:
	case STT_OP_MulEq:
	case STT_OP_DivEq:
	case STT_OP_ModEq:
	case STT_OP_AndEq:
	case STT_OP_OrEq:
	case STT_OP_XorEq:
	case STT_OP_LshEq:
	case STT_OP_RshEq:
		return 0;

	default:
		return 5; // Default precedence for unknown operators
	}
}

